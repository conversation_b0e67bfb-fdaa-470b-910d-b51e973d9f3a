#!/usr/bin/env python3
"""
Test script to crawl a single Costco tire page and see the data structure
"""

import asyncio
import json
from datetime import datetime
from crawl4ai import AsyncWebCrawler
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_single_page():
    """Test crawling a single page to understand the data structure"""
    url = "https://www.costco.com.tw/Tire/c/142201?page=0"

    # Define extraction strategy for Costco Taiwan tire data
    extraction_strategy = JsonCssExtractionStrategy({
        "tires": [
            {
                "selector": ".product-tile, .product-item, .product, [data-product-id], .item",
                "fields": {
                    "category": {"selector": ".category, .product-category", "attribute": "text"},
                    "login_price_notice": {"selector": ".login-price, .member-price, .price-login", "attribute": "text"},
                    "chinese_name": {"selector": ".product-name-chinese, .name-chinese", "attribute": "text"},
                    "english_name": {"selector": ".product-name-english, .name-english", "attribute": "text"},
                    "full_name": {"selector": ".product-name, .product-title, h3, h4, .name", "attribute": "text"},
                    "tire_size": {"selector": ".tire-size, .size-info, .specifications", "attribute": "text"},
                    "speed_rating": {"selector": ".speed-rating, .rating-info", "attribute": "text"},
                    "installation_note": {"selector": ".installation-info, .service-note, .note", "attribute": "text"},
                    "purchase_limit": {"selector": ".purchase-limit, .limit-info, .quantity-limit", "attribute": "text"},
                    "rating_stars": {"selector": ".rating, .stars, .review-stars", "attribute": "text"},
                    "min_quantity": {"selector": ".min-qty, .minimum-order", "attribute": "text"},
                    "max_quantity": {"selector": ".max-qty, .maximum-order", "attribute": "text"},
                    "price": {"selector": ".price, .product-price, .cost, .amount", "attribute": "text"},
                    "image": {"selector": "img", "attribute": "src"},
                    "product_link": {"selector": "a", "attribute": "href"},
                    "product_id": {"selector": "[data-product-id]", "attribute": "data-product-id"},
                    "brand": {"selector": ".brand, .manufacturer, .brand-name", "attribute": "text"},
                    "model": {"selector": ".model, .product-model, .model-name", "attribute": "text"},
                    "availability": {"selector": ".availability, .stock, .in-stock", "attribute": "text"},
                    "compare_option": {"selector": ".compare, .add-to-compare", "attribute": "text"},
                    "all_text": {"selector": "", "attribute": "text"}  # Get all text content
                }
            }
        ],
        "page_info": {
            "title": {"selector": "title", "attribute": "text"},
            "total_results": {"selector": ".results-count, .total-count, .result-info", "attribute": "text"},
            "category_name": {"selector": ".category-title, .breadcrumb", "attribute": "text"}
        }
    })

    async with AsyncWebCrawler(verbose=True) as crawler:
        try:
            logger.info(f"Testing page: {url}")

            result = await crawler.arun(
                url=url,
                extraction_strategy=extraction_strategy,
                wait_for="networkidle",
                timeout=60000,
                js_code="""
                // Wait for content to load
                await new Promise(resolve => setTimeout(resolve, 5000));

                // Scroll to load more content if needed
                window.scrollTo(0, document.body.scrollHeight);
                await new Promise(resolve => setTimeout(resolve, 3000));

                // Try to trigger any lazy loading
                window.scrollTo(0, 0);
                await new Promise(resolve => setTimeout(resolve, 2000));
                window.scrollTo(0, document.body.scrollHeight);
                await new Promise(resolve => setTimeout(resolve, 3000));

                // Log what we can see
                console.log('Page title:', document.title);
                console.log('Body classes:', document.body.className);
                console.log('Number of divs:', document.querySelectorAll('div').length);
                console.log('Number of products found:', document.querySelectorAll('[data-product], .product, .item').length);
                """
            )

            if result.success:
                # Save extracted data
                extracted_data = json.loads(result.extracted_content) if result.extracted_content else {}

                output_data = {
                    "url": url,
                    "timestamp": datetime.now().isoformat(),
                    "extracted_data": extracted_data,
                    "html_length": len(result.html) if result.html else 0,
                    "markdown_length": len(result.markdown) if result.markdown else 0
                }

                # Save to file
                with open("test_single_page_result.json", 'w', encoding='utf-8') as f:
                    json.dump(output_data, f, indent=2, ensure_ascii=False)

                # Also save raw HTML for inspection
                if result.html:
                    with open("test_page_raw.html", 'w', encoding='utf-8') as f:
                        f.write(result.html)

                # Also save markdown for inspection
                if result.markdown:
                    with open("test_page_markdown.md", 'w', encoding='utf-8') as f:
                        f.write(result.markdown)

                logger.info("Test completed successfully!")
                logger.info(f"Extracted data saved to: test_single_page_result.json")
                logger.info(f"Raw HTML saved to: test_page_raw.html")
                logger.info(f"Markdown saved to: test_page_markdown.md")

                # Print summary
                print(f"\n=== TEST RESULTS ===")
                print(f"URL: {url}")
                print(f"Success: {result.success}")
                print(f"HTML length: {len(result.html) if result.html else 0}")
                print(f"Markdown length: {len(result.markdown) if result.markdown else 0}")
                print(f"Extracted tires count: {len(extracted_data.get('tires', []))}")

                if extracted_data.get('tires'):
                    print(f"\nFirst tire example:")
                    first_tire = extracted_data['tires'][0]
                    for key, value in first_tire.items():
                        if value:  # Only show non-empty values
                            print(f"  {key}: {value}")

            else:
                logger.error(f"Failed to crawl page: {result.error_message}")

        except Exception as e:
            logger.error(f"Exception during test: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_single_page())
