#!/usr/bin/env python3
"""
Costco Tire Pages Crawler using crawl4ai
Extracts data from pages 0-100 and saves all JSON responses to a single text file
"""

import asyncio
import json
import time
from datetime import datetime
from crawl4ai import AsyncWebCrawler
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CostcoTireCrawler:
    def __init__(self):
        self.base_url = "https://www.costco.com.tw/Tire/c/142201?page="
        self.all_responses = []
        self.failed_pages = []
        
    async def crawl_page(self, session, page_num):
        """Crawl a single page and extract JSON data"""
        url = f"{self.base_url}{page_num}"
        
        try:
            logger.info(f"Crawling page {page_num}: {url}")
            
            # Define extraction strategy for tire data
            extraction_strategy = JsonCssExtractionStrategy({
                "tires": [
                    {
                        "selector": ".product-item, .product-card, [data-product]",
                        "fields": {
                            "name": {"selector": ".product-name, .product-title, h3, h4", "attribute": "text"},
                            "price": {"selector": ".price, .product-price, .cost", "attribute": "text"},
                            "brand": {"selector": ".brand, .manufacturer", "attribute": "text"},
                            "model": {"selector": ".model, .product-model", "attribute": "text"},
                            "size": {"selector": ".size, .tire-size", "attribute": "text"},
                            "image": {"selector": "img", "attribute": "src"},
                            "link": {"selector": "a", "attribute": "href"},
                            "availability": {"selector": ".availability, .stock", "attribute": "text"},
                            "rating": {"selector": ".rating, .stars", "attribute": "text"},
                            "description": {"selector": ".description, .product-desc", "attribute": "text"}
                        }
                    }
                ],
                "pagination": {
                    "current_page": {"selector": ".current-page, .active", "attribute": "text"},
                    "total_pages": {"selector": ".total-pages, .page-count", "attribute": "text"},
                    "next_page": {"selector": ".next-page, .pagination-next", "attribute": "href"}
                },
                "page_info": {
                    "title": {"selector": "title", "attribute": "text"},
                    "total_results": {"selector": ".results-count, .total-count", "attribute": "text"}
                }
            })
            
            result = await session.arun(
                url=url,
                extraction_strategy=extraction_strategy,
                wait_for="networkidle",
                timeout=30000,
                js_code="""
                // Wait for content to load
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Scroll to load more content if needed
                window.scrollTo(0, document.body.scrollHeight);
                await new Promise(resolve => setTimeout(resolve, 1000));
                """
            )
            
            if result.success:
                page_data = {
                    "page_number": page_num,
                    "url": url,
                    "timestamp": datetime.now().isoformat(),
                    "extracted_data": json.loads(result.extracted_content) if result.extracted_content else {},
                    "html_length": len(result.html) if result.html else 0,
                    "markdown_length": len(result.markdown) if result.markdown else 0
                }
                
                self.all_responses.append(page_data)
                logger.info(f"Successfully crawled page {page_num}")
                return True
            else:
                logger.error(f"Failed to crawl page {page_num}: {result.error_message}")
                self.failed_pages.append({"page": page_num, "error": result.error_message})
                return False
                
        except Exception as e:
            logger.error(f"Exception while crawling page {page_num}: {str(e)}")
            self.failed_pages.append({"page": page_num, "error": str(e)})
            return False
    
    async def crawl_all_pages(self, start_page=0, end_page=100, batch_size=5):
        """Crawl all pages from start_page to end_page"""
        logger.info(f"Starting to crawl pages {start_page} to {end_page}")
        
        async with AsyncWebCrawler(verbose=True) as crawler:
            # Process pages in batches to avoid overwhelming the server
            for batch_start in range(start_page, end_page + 1, batch_size):
                batch_end = min(batch_start + batch_size - 1, end_page)
                logger.info(f"Processing batch: pages {batch_start} to {batch_end}")
                
                # Create tasks for current batch
                tasks = []
                for page_num in range(batch_start, batch_end + 1):
                    tasks.append(self.crawl_page(crawler, page_num))
                
                # Execute batch
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Log batch results
                successful = sum(1 for r in results if r is True)
                logger.info(f"Batch completed: {successful}/{len(tasks)} pages successful")
                
                # Add delay between batches to be respectful
                if batch_end < end_page:
                    logger.info("Waiting 3 seconds before next batch...")
                    await asyncio.sleep(3)
        
        logger.info(f"Crawling completed. Total pages processed: {len(self.all_responses)}")
        logger.info(f"Failed pages: {len(self.failed_pages)}")
    
    def save_to_file(self, filename="costco_tire_data.json"):
        """Save all collected data to a JSON file"""
        output_data = {
            "crawl_info": {
                "total_pages_attempted": 101,  # 0-100
                "successful_pages": len(self.all_responses),
                "failed_pages": len(self.failed_pages),
                "crawl_timestamp": datetime.now().isoformat(),
                "base_url": self.base_url
            },
            "failed_pages": self.failed_pages,
            "page_data": self.all_responses
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Data saved to {filename}")
            logger.info(f"File size: {len(json.dumps(output_data, ensure_ascii=False))} characters")
            
        except Exception as e:
            logger.error(f"Error saving to file: {str(e)}")

async def main():
    """Main function to run the crawler"""
    crawler = CostcoTireCrawler()
    
    try:
        # Crawl all pages (0-100)
        await crawler.crawl_all_pages(start_page=0, end_page=100, batch_size=3)
        
        # Save results to file
        crawler.save_to_file("costco_tire_data.json")
        
        # Print summary
        print(f"\n=== CRAWLING SUMMARY ===")
        print(f"Total pages attempted: 101 (pages 0-100)")
        print(f"Successful pages: {len(crawler.all_responses)}")
        print(f"Failed pages: {len(crawler.failed_pages)}")
        
        if crawler.failed_pages:
            print(f"\nFailed pages:")
            for failed in crawler.failed_pages:
                print(f"  Page {failed['page']}: {failed['error']}")
        
        print(f"\nData saved to: costco_tire_data.json")
        
    except KeyboardInterrupt:
        print("\nCrawling interrupted by user")
        crawler.save_to_file("costco_tire_data_partial.json")
    except Exception as e:
        print(f"Error during crawling: {str(e)}")
        crawler.save_to_file("costco_tire_data_error.json")

if __name__ == "__main__":
    asyncio.run(main())
