#!/usr/bin/env python3
"""
Final Costco Tire Crawler - Extract tire data from pages 0-22 using the successful approach
"""

import asyncio
import json
import re
from datetime import datetime
from crawl4ai import AsyncWebCrawler
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalCostcoTireCrawler:
    def __init__(self):
        self.base_url = "https://www.costco.com.tw/Tire/c/142201?page="
        self.all_responses = []
        self.failed_pages = []
        
    def parse_tire_data_from_markdown(self, markdown_text):
        """Parse tire data from markdown text using the successful pattern"""
        tires = []
        lines = markdown_text.split('\n')
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # Look for tire product image/link pattern
            if ('[![' in line and '輪胎' in line and 'medias/sys_master/images' in line):
                tire = {}
                
                # Extract image URL and product URL from the markdown link
                img_match = re.search(r'!\[([^]]*輪胎[^]]*)\]\(([^)]*)\)', line)
                if img_match:
                    tire['chinese_name'] = img_match.group(1)
                    tire['image_url'] = img_match.group(2)
                
                # Extract product URL
                url_match = re.search(r'\]\(([^)]*tire/p/[^)]*)\)', line)
                if url_match:
                    tire['product_url'] = url_match.group(1)
                
                # Look ahead for the tire details
                j = i + 1
                while j < len(lines) and j < i + 20:  # Look within next 20 lines
                    next_line = lines[j].strip()
                    
                    # Category
                    if next_line == '輪胎':
                        tire['category'] = next_line
                    
                    # Price notice
                    elif next_line == '登入顯示售價':
                        tire['price_notice'] = next_line
                    
                    # Chinese and English names in links
                    elif ('[' in next_line and '](' in next_line and '輪胎' in next_line and 'Tire' in next_line):
                        # Extract both Chinese and English names
                        chinese_match = re.search(r'\[([^]]*輪胎[^]]*)\]', next_line)
                        english_match = re.search(r'\[([^]]*Tire[^]]*)\]', next_line)
                        
                        if chinese_match:
                            tire['chinese_name'] = chinese_match.group(1)
                        if english_match:
                            tire['english_name'] = english_match.group(1)
                    
                    # Tire Size
                    elif next_line == '* Tire Size :' and j + 1 < len(lines):
                        tire['tire_size'] = lines[j + 1].strip().replace('* ', '')
                    
                    # Season type
                    elif next_line == '* 季節輪胎 :' and j + 1 < len(lines):
                        tire['season_type'] = lines[j + 1].strip().replace('* ', '')
                    
                    # Speed rating
                    elif next_line == '* 速度等級 :' and j + 1 < len(lines):
                        tire['speed_rating'] = lines[j + 1].strip().replace('* ', '')
                    
                    # Installation note
                    elif '*本商品輪胎安裝皆為於您指定的賣場輪胎中心安裝' in next_line:
                        tire['installation_note'] = next_line
                    
                    # Purchase limit
                    elif '*每次限購' in next_line and '顆' in next_line:
                        tire['purchase_limit'] = next_line
                    
                    # Rating stars
                    elif '★' in next_line:
                        tire['rating_stars'] = next_line.count('★')
                        tire['rating_display'] = next_line
                    
                    # Min/Max quantities
                    elif '最小訂購數量：' in next_line:
                        min_qty = re.search(r'最小訂購數量：(\d+)', next_line)
                        if min_qty:
                            tire['min_quantity'] = int(min_qty.group(1))
                    
                    elif '最大訂購數量：' in next_line:
                        max_qty = re.search(r'最大訂購數量：(\d+)', next_line)
                        if max_qty:
                            tire['max_quantity'] = int(max_qty.group(1))
                    
                    # Stop when we reach the next product or end of current product
                    elif ('加入比較產品清單' in next_line or 
                          ('[![' in next_line and '輪胎' in next_line and 'medias/sys_master/images' in next_line)):
                        break
                    
                    j += 1
                
                # Only add tire if it has essential information
                if len(tire) > 3:  # At least image, name, and some details
                    tires.append(tire)
                
                i = j  # Skip to where we left off
            else:
                i += 1
        
        return tires
    
    async def crawl_page(self, session, page_num):
        """Crawl a single page and extract tire data"""
        url = f"{self.base_url}{page_num}"
        
        try:
            logger.info(f"Crawling page {page_num}: {url}")
            
            result = await session.arun(
                url=url,
                wait_for="networkidle",
                timeout=60000,
                js_code="""
                // Wait for content to load
                await new Promise(resolve => setTimeout(resolve, 8000));
                
                // Scroll to load more content
                window.scrollTo(0, document.body.scrollHeight);
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                // Scroll back up
                window.scrollTo(0, 0);
                await new Promise(resolve => setTimeout(resolve, 2000));
                """
            )
            
            if result.success:
                markdown_text = result.markdown if result.markdown else ""
                tires = self.parse_tire_data_from_markdown(markdown_text)
                
                page_data = {
                    "page_number": page_num,
                    "url": url,
                    "timestamp": datetime.now().isoformat(),
                    "tires": tires,
                    "tire_count": len(tires),
                    "html_length": len(result.html) if result.html else 0,
                    "markdown_length": len(markdown_text)
                }
                
                self.all_responses.append(page_data)
                logger.info(f"Successfully crawled page {page_num}, found {len(tires)} tires")
                return True
            else:
                logger.error(f"Failed to crawl page {page_num}: {result.error_message}")
                self.failed_pages.append({"page": page_num, "error": result.error_message})
                return False
                
        except Exception as e:
            logger.error(f"Exception while crawling page {page_num}: {str(e)}")
            self.failed_pages.append({"page": page_num, "error": str(e)})
            return False
    
    async def crawl_all_pages(self, start_page=0, end_page=22, batch_size=3):
        """Crawl all pages from start_page to end_page"""
        logger.info(f"Starting to crawl pages {start_page} to {end_page}")
        
        async with AsyncWebCrawler(verbose=True) as crawler:
            for batch_start in range(start_page, end_page + 1, batch_size):
                batch_end = min(batch_start + batch_size - 1, end_page)
                logger.info(f"Processing batch: pages {batch_start} to {batch_end}")
                
                tasks = []
                for page_num in range(batch_start, batch_end + 1):
                    tasks.append(self.crawl_page(crawler, page_num))
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                successful = sum(1 for r in results if r is True)
                logger.info(f"Batch completed: {successful}/{len(tasks)} pages successful")
                
                # Save intermediate results
                intermediate_filename = f"final_tire_data_batch_{batch_start}-{batch_end}.json"
                self.save_intermediate_results(intermediate_filename)
                
                if batch_end < end_page:
                    logger.info("Waiting 3 seconds before next batch...")
                    await asyncio.sleep(3)
        
        logger.info(f"Crawling completed. Total pages processed: {len(self.all_responses)}")
        logger.info(f"Failed pages: {len(self.failed_pages)}")
    
    def save_intermediate_results(self, filename):
        """Save intermediate results"""
        total_tires = sum(page.get('tire_count', 0) for page in self.all_responses)
        
        output_data = {
            "crawl_info": {
                "pages_completed_so_far": len(self.all_responses),
                "failed_pages_so_far": len(self.failed_pages),
                "total_tires_found": total_tires,
                "timestamp": datetime.now().isoformat(),
                "base_url": self.base_url
            },
            "failed_pages": self.failed_pages,
            "page_data": self.all_responses
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            logger.info(f"Intermediate results saved to {filename} - {total_tires} tires found so far")
        except Exception as e:
            logger.error(f"Error saving intermediate results: {str(e)}")
    
    def save_final_results(self, filename="final_costco_tire_data.json"):
        """Save final results"""
        total_tires = sum(page.get('tire_count', 0) for page in self.all_responses)
        
        output_data = {
            "crawl_info": {
                "total_pages_attempted": 23,  # 0-22
                "successful_pages": len(self.all_responses),
                "failed_pages": len(self.failed_pages),
                "total_tires_found": total_tires,
                "crawl_timestamp": datetime.now().isoformat(),
                "base_url": self.base_url
            },
            "failed_pages": self.failed_pages,
            "page_data": self.all_responses
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Final data saved to {filename}")
            logger.info(f"Total tires found: {total_tires}")
            
        except Exception as e:
            logger.error(f"Error saving final results: {str(e)}")

async def main():
    """Main function"""
    crawler = FinalCostcoTireCrawler()
    
    try:
        await crawler.crawl_all_pages(start_page=0, end_page=22, batch_size=3)
        crawler.save_final_results("final_costco_tire_data.json")
        
        total_tires = sum(page.get('tire_count', 0) for page in crawler.all_responses)
        print(f"\n=== FINAL CRAWLING SUMMARY ===")
        print(f"Total pages attempted: 23 (pages 0-22)")
        print(f"Successful pages: {len(crawler.all_responses)}")
        print(f"Failed pages: {len(crawler.failed_pages)}")
        print(f"Total tires found: {total_tires}")
        
        if crawler.failed_pages:
            print(f"\nFailed pages:")
            for failed in crawler.failed_pages:
                print(f"  Page {failed['page']}: {failed['error']}")
        
        print(f"\nFinal data saved to: final_costco_tire_data.json")
        print(f"Intermediate files were also saved for each batch")
        
    except KeyboardInterrupt:
        print("\nCrawling interrupted by user")
        crawler.save_final_results("final_costco_tire_data_partial.json")
    except Exception as e:
        print(f"Error during crawling: {str(e)}")
        crawler.save_final_results("final_costco_tire_data_error.json")

if __name__ == "__main__":
    asyncio.run(main())
