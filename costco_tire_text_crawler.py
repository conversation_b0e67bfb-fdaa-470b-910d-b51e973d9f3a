#!/usr/bin/env python3
"""
Costco Tire Text-Based Crawler using crawl4ai
Extracts tire data from markdown text for pages 0-22 and saves all responses to files
"""

import asyncio
import json
import re
import time
from datetime import datetime
from crawl4ai import AsyncWebCrawler
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CostcoTireTextCrawler:
    def __init__(self):
        self.base_url = "https://www.costco.com.tw/Tire/c/142201?page="
        self.all_responses = []
        self.failed_pages = []
        
    def parse_tire_data_from_text(self, text):
        """Parse tire data from markdown text"""
        tires = []
        
        # Split text into lines for processing
        lines = text.split('\n')
        
        current_tire = {}
        in_tire_section = False
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # Look for tire product patterns
            if '輪胎' in line and ('[![' in line or '[' in line) and 'Tire' in line:
                # Save previous tire if exists
                if current_tire and len(current_tire) > 2:
                    tires.append(current_tire)
                
                # Start new tire
                current_tire = {}
                in_tire_section = True
                
                # Extract Chinese and English names from the link
                chinese_match = re.search(r'\[([^]]*輪胎[^]]*)\]', line)
                english_match = re.search(r'\[([^]]*Tire[^]]*)\]', line)
                
                if chinese_match:
                    current_tire['chinese_name'] = chinese_match.group(1)
                if english_match:
                    current_tire['english_name'] = english_match.group(1)
                
                # Extract product URL
                url_match = re.search(r'\(([^)]*tire/p/[^)]*)\)', line)
                if url_match:
                    current_tire['product_url'] = url_match.group(1)
                
                # Extract image URL
                img_match = re.search(r'!\[.*?\]\(([^)]*)\)', line)
                if img_match:
                    current_tire['image_url'] = img_match.group(1)
                
                continue
            
            if in_tire_section:
                # Look for specific tire information
                if 'Tire Size :' in line:
                    # Next line should contain the size
                    if i + 1 < len(lines):
                        size_line = lines[i + 1].strip()
                        if size_line.startswith('*'):
                            size_line = size_line[1:].strip()
                        current_tire['tire_size'] = size_line
                
                elif '季節輪胎 :' in line:
                    # Next line should contain the season type
                    if i + 1 < len(lines):
                        season_line = lines[i + 1].strip()
                        if season_line.startswith('*'):
                            season_line = season_line[1:].strip()
                        current_tire['season_type'] = season_line
                
                elif '速度等級 :' in line:
                    # Next line should contain the speed rating
                    if i + 1 < len(lines):
                        speed_line = lines[i + 1].strip()
                        if speed_line.startswith('*'):
                            speed_line = speed_line[1:].strip()
                        current_tire['speed_rating'] = speed_line
                
                elif '*本商品輪胎安裝皆為於您指定的賣場輪胎中心安裝' in line:
                    current_tire['installation_note'] = line
                
                elif '*每次限購' in line and '顆' in line:
                    current_tire['purchase_limit'] = line
                
                elif '最小訂購數量：' in line:
                    min_qty = re.search(r'最小訂購數量：(\d+)', line)
                    if min_qty:
                        current_tire['min_quantity'] = int(min_qty.group(1))
                
                elif '最大訂購數量：' in line:
                    max_qty = re.search(r'最大訂購數量：(\d+)', line)
                    if max_qty:
                        current_tire['max_quantity'] = int(max_qty.group(1))
                
                elif '★' in line:
                    # Count stars for rating
                    star_count = line.count('★')
                    if star_count > 0:
                        current_tire['rating_stars'] = star_count
                
                elif '登入顯示售價' in line:
                    current_tire['price_note'] = line
                
                # Check if we've moved to next product
                elif line == '加入比較產品清單' and current_tire:
                    in_tire_section = False
        
        # Add the last tire if exists
        if current_tire and len(current_tire) > 2:
            tires.append(current_tire)
        
        return tires
    
    async def crawl_page(self, session, page_num):
        """Crawl a single page and extract tire data from text"""
        url = f"{self.base_url}{page_num}"
        
        try:
            logger.info(f"Crawling page {page_num}: {url}")
            
            result = await session.arun(
                url=url,
                wait_for="networkidle",
                timeout=60000,
                js_code="""
                // Wait for content to load
                await new Promise(resolve => setTimeout(resolve, 8000));
                
                // Scroll to load more content
                window.scrollTo(0, document.body.scrollHeight);
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                // Scroll back up
                window.scrollTo(0, 0);
                await new Promise(resolve => setTimeout(resolve, 2000));
                """
            )
            
            if result.success:
                # Extract tire data from markdown text
                markdown_text = result.markdown if result.markdown else ""
                tires = self.parse_tire_data_from_text(markdown_text)
                
                page_data = {
                    "page_number": page_num,
                    "url": url,
                    "timestamp": datetime.now().isoformat(),
                    "tires": tires,
                    "tire_count": len(tires),
                    "html_length": len(result.html) if result.html else 0,
                    "markdown_length": len(markdown_text)
                }
                
                self.all_responses.append(page_data)
                logger.info(f"Successfully crawled page {page_num}, found {len(tires)} tires")
                return True
            else:
                logger.error(f"Failed to crawl page {page_num}: {result.error_message}")
                self.failed_pages.append({"page": page_num, "error": result.error_message})
                return False
                
        except Exception as e:
            logger.error(f"Exception while crawling page {page_num}: {str(e)}")
            self.failed_pages.append({"page": page_num, "error": str(e)})
            return False
    
    async def crawl_all_pages(self, start_page=0, end_page=22, batch_size=3):
        """Crawl all pages from start_page to end_page"""
        logger.info(f"Starting to crawl pages {start_page} to {end_page}")
        
        async with AsyncWebCrawler(verbose=True) as crawler:
            # Process pages in batches to avoid overwhelming the server
            for batch_start in range(start_page, end_page + 1, batch_size):
                batch_end = min(batch_start + batch_size - 1, end_page)
                logger.info(f"Processing batch: pages {batch_start} to {batch_end}")
                
                # Create tasks for current batch
                tasks = []
                for page_num in range(batch_start, batch_end + 1):
                    tasks.append(self.crawl_page(crawler, page_num))
                
                # Execute batch
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Log batch results
                successful = sum(1 for r in results if r is True)
                logger.info(f"Batch completed: {successful}/{len(tasks)} pages successful")
                
                # Save intermediate results after each batch
                intermediate_filename = f"costco_tire_text_batch_{batch_start}-{batch_end}.json"
                self.save_intermediate_results(intermediate_filename)
                
                # Add delay between batches to be respectful
                if batch_end < end_page:
                    logger.info("Waiting 3 seconds before next batch...")
                    await asyncio.sleep(3)
        
        logger.info(f"Crawling completed. Total pages processed: {len(self.all_responses)}")
        logger.info(f"Failed pages: {len(self.failed_pages)}")
    
    def save_intermediate_results(self, filename):
        """Save intermediate results after each batch"""
        output_data = {
            "crawl_info": {
                "pages_completed_so_far": len(self.all_responses),
                "failed_pages_so_far": len(self.failed_pages),
                "total_tires_found": sum(page.get('tire_count', 0) for page in self.all_responses),
                "timestamp": datetime.now().isoformat(),
                "base_url": self.base_url
            },
            "failed_pages": self.failed_pages,
            "page_data": self.all_responses
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            logger.info(f"Intermediate results saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving intermediate results: {str(e)}")
    
    def save_to_file(self, filename="costco_tire_text_data.json"):
        """Save all collected data to a JSON file"""
        total_tires = sum(page.get('tire_count', 0) for page in self.all_responses)
        
        output_data = {
            "crawl_info": {
                "total_pages_attempted": 23,  # 0-22
                "successful_pages": len(self.all_responses),
                "failed_pages": len(self.failed_pages),
                "total_tires_found": total_tires,
                "crawl_timestamp": datetime.now().isoformat(),
                "base_url": self.base_url
            },
            "failed_pages": self.failed_pages,
            "page_data": self.all_responses
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Data saved to {filename}")
            logger.info(f"Total tires found: {total_tires}")
            
        except Exception as e:
            logger.error(f"Error saving to file: {str(e)}")

async def main():
    """Main function to run the crawler"""
    crawler = CostcoTireTextCrawler()
    
    try:
        # Crawl all pages (0-22)
        await crawler.crawl_all_pages(start_page=0, end_page=22, batch_size=3)
        
        # Save results to file
        crawler.save_to_file("costco_tire_text_data.json")
        
        # Print summary
        total_tires = sum(page.get('tire_count', 0) for page in crawler.all_responses)
        print(f"\n=== CRAWLING SUMMARY ===")
        print(f"Total pages attempted: 23 (pages 0-22)")
        print(f"Successful pages: {len(crawler.all_responses)}")
        print(f"Failed pages: {len(crawler.failed_pages)}")
        print(f"Total tires found: {total_tires}")
        
        if crawler.failed_pages:
            print(f"\nFailed pages:")
            for failed in crawler.failed_pages:
                print(f"  Page {failed['page']}: {failed['error']}")
        
        print(f"\nData saved to: costco_tire_text_data.json")
        print(f"Intermediate files were also saved for each batch")
        
    except KeyboardInterrupt:
        print("\nCrawling interrupted by user")
        crawler.save_to_file("costco_tire_text_data_partial.json")
    except Exception as e:
        print(f"Error during crawling: {str(e)}")
        crawler.save_to_file("costco_tire_text_data_error.json")

if __name__ == "__main__":
    asyncio.run(main())
