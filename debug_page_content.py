#!/usr/bin/env python3
"""
Debug script to examine the actual content from crawled pages
"""

import asyncio
import json
from datetime import datetime
from crawl4ai import AsyncWebCrawler
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def debug_page_content():
    """Debug the actual content from a single page"""
    url = "https://www.costco.com.tw/Tire/c/142201?page=0"
    
    async with AsyncWebCrawler(verbose=True) as crawler:
        try:
            logger.info(f"Debugging page: {url}")
            
            result = await crawler.arun(
                url=url,
                wait_for="networkidle",
                timeout=60000,
                js_code="""
                // Wait for content to load
                await new Promise(resolve => setTimeout(resolve, 8000));
                
                // Scroll to load more content
                window.scrollTo(0, document.body.scrollHeight);
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                // Scroll back up
                window.scrollTo(0, 0);
                await new Promise(resolve => setTimeout(resolve, 2000));
                """
            )
            
            if result.success:
                markdown_text = result.markdown if result.markdown else ""
                
                # Save the full markdown for inspection
                with open("debug_page_markdown.md", 'w', encoding='utf-8') as f:
                    f.write(markdown_text)
                
                # Look for tire-related patterns
                lines = markdown_text.split('\n')
                tire_patterns = []
                
                for i, line in enumerate(lines):
                    line_lower = line.lower()
                    if any(keyword in line_lower for keyword in ['輪胎', 'tire', 'bridgestone', 'michelin', 'continental', 'pirelli', 'goodyear']):
                        tire_patterns.append({
                            'line_number': i,
                            'content': line.strip()
                        })
                
                # Save debug info
                debug_data = {
                    "url": url,
                    "timestamp": datetime.now().isoformat(),
                    "markdown_length": len(markdown_text),
                    "total_lines": len(lines),
                    "tire_patterns_found": len(tire_patterns),
                    "tire_patterns": tire_patterns[:20],  # First 20 patterns
                    "sample_lines": lines[100:150] if len(lines) > 150 else lines  # Sample lines
                }
                
                with open("debug_page_analysis.json", 'w', encoding='utf-8') as f:
                    json.dump(debug_data, f, indent=2, ensure_ascii=False)
                
                logger.info("Debug completed!")
                logger.info(f"Markdown saved to: debug_page_markdown.md")
                logger.info(f"Analysis saved to: debug_page_analysis.json")
                
                print(f"\n=== DEBUG RESULTS ===")
                print(f"URL: {url}")
                print(f"Markdown length: {len(markdown_text)}")
                print(f"Total lines: {len(lines)}")
                print(f"Tire patterns found: {len(tire_patterns)}")
                
                if tire_patterns:
                    print(f"\nFirst 10 tire-related patterns:")
                    for i, pattern in enumerate(tire_patterns[:10]):
                        print(f"{i+1}. Line {pattern['line_number']}: {pattern['content']}")
                
                # Show some sample lines to understand structure
                print(f"\nSample lines (100-120):")
                for i, line in enumerate(lines[100:120], 100):
                    if line.strip():
                        print(f"{i}: {line}")
                
            else:
                logger.error(f"Failed to debug page: {result.error_message}")
                
        except Exception as e:
            logger.error(f"Exception during debug: {str(e)}")

if __name__ == "__main__":
    asyncio.run(debug_page_content())
