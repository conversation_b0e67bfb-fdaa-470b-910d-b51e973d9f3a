#!/usr/bin/env python3
"""
Robust Costco Tire Crawler - Wait for dynamic content to load properly
Target: 48 tires per page × 21 pages = ~1008 tires
"""

import asyncio
import json
import re
from datetime import datetime
from crawl4ai import AsyncWebCrawler
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RobustCostcoTireCrawler:
    def __init__(self):
        self.base_url = "https://www.costco.com.tw/Tire/c/142201?page="
        self.all_tire_data = []
        self.failed_pages = []
        
    def extract_tire_data_comprehensive(self, text):
        """Extract tire data using multiple patterns"""
        tires = []
        lines = text.split('\n')
        
        # Look for tire product patterns
        for i, line in enumerate(lines):
            line = line.strip()
            
            # Pattern 1: Image links with tire names
            if ('[![' in line and '輪胎' in line and 'medias/sys_master/images' in line):
                tire = self.extract_single_tire_from_context(lines, i)
                if tire:
                    tires.append(tire)
            
            # Pattern 2: Direct tire name links
            elif ('[' in line and '](' in line and '輪胎' in line and 'tire/p/' in line):
                tire = self.extract_single_tire_from_context(lines, i)
                if tire:
                    tires.append(tire)
        
        return tires
    
    def extract_single_tire_from_context(self, lines, center_index):
        """Extract a single tire's data from surrounding context"""
        tire = {}
        
        # Look at surrounding lines (±10 lines)
        start_idx = max(0, center_index - 10)
        end_idx = min(len(lines), center_index + 10)
        context = lines[start_idx:end_idx]
        
        for i, line in enumerate(context):
            line = line.strip()
            
            # Image and product link
            if ('[![' in line and '輪胎' in line and 'medias/sys_master/images' in line):
                tire['image_and_product_link'] = line
            
            # Category
            elif line == '輪胎':
                tire['category'] = line
            
            # Price notice
            elif line == '登入顯示售價':
                tire['price_notice'] = line
            
            # Chinese and English names
            elif ('[' in line and '](' in line and ('輪胎' in line or 'Tire' in line)):
                if 'chinese_and_english_names' not in tire:
                    tire['chinese_and_english_names'] = line
                else:
                    tire['chinese_and_english_names'] += line
            
            # Tire specifications
            elif 'Tire Size :' in line and i + 1 < len(context):
                tire['tire_size'] = f"* Tire Size : {context[i + 1].strip()}"
            
            elif '季節輪胎 :' in line and i + 1 < len(context):
                tire['season_type'] = f"* 季節輪胎 : {context[i + 1].strip()}"
            
            elif '速度等級 :' in line and i + 1 < len(context):
                tire['speed_rating'] = f"* 速度等級 : {context[i + 1].strip()}"
            
            # Installation and purchase info
            elif '*本商品輪胎安裝皆為於您指定的賣場輪胎中心安裝' in line:
                tire['installation_note'] = line
            
            elif '*每次限購' in line and '顆' in line:
                tire['purchase_limit'] = line
            
            # Rating
            elif '★' in line and len(line) <= 30:
                tire['rating'] = line
            
            # Quantities
            elif '最小訂購數量：' in line:
                tire['min_quantity'] = line
            elif '最大訂購數量：' in line:
                tire['max_quantity'] = line
        
        # Only return if we have substantial data
        return tire if len(tire) >= 3 else None
    
    async def crawl_page_robust(self, session, page_num):
        """Crawl a single page with robust dynamic content loading"""
        url = f"{self.base_url}{page_num}"
        
        try:
            logger.info(f"Robustly crawling page {page_num}: {url}")
            
            result = await session.arun(
                url=url,
                wait_for="networkidle",
                timeout=120000,  # 2 minutes timeout
                js_code="""
                // Function to wait for elements
                async function waitForElements(selector, timeout = 30000) {
                    const start = Date.now();
                    while (Date.now() - start < timeout) {
                        const elements = document.querySelectorAll(selector);
                        if (elements.length > 0) {
                            console.log(`Found ${elements.length} elements with selector: ${selector}`);
                            return elements;
                        }
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                    console.log(`Timeout waiting for selector: ${selector}`);
                    return [];
                }
                
                // Wait for initial page load
                console.log('Starting robust tire page crawl...');
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                // Try multiple selectors for tire products
                const selectors = [
                    '.product-item',
                    '.product-tile', 
                    '.product-card',
                    '[data-product]',
                    '.item',
                    '.tire-product',
                    '.product'
                ];
                
                let foundProducts = false;
                for (const selector of selectors) {
                    const elements = await waitForElements(selector, 10000);
                    if (elements.length > 0) {
                        console.log(`Found ${elements.length} products with selector: ${selector}`);
                        foundProducts = true;
                        break;
                    }
                }
                
                if (!foundProducts) {
                    console.log('No products found with standard selectors, trying aggressive scrolling...');
                    
                    // Aggressive scrolling to trigger lazy loading
                    for (let i = 0; i < 10; i++) {
                        window.scrollTo(0, i * 500);
                        await new Promise(resolve => setTimeout(resolve, 2000));
                    }
                    
                    // Scroll to bottom
                    window.scrollTo(0, document.body.scrollHeight);
                    await new Promise(resolve => setTimeout(resolve, 5000));
                    
                    // Scroll back to top
                    window.scrollTo(0, 0);
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    
                    // Try clicking any "load more" or pagination buttons
                    const loadMoreButtons = document.querySelectorAll('button, a, .load-more, .show-more, .pagination');
                    console.log(`Found ${loadMoreButtons.length} potential load buttons`);
                    
                    for (const button of loadMoreButtons) {
                        if (button.textContent.includes('更多') || button.textContent.includes('載入') || button.textContent.includes('顯示')) {
                            console.log(`Clicking button: ${button.textContent}`);
                            button.click();
                            await new Promise(resolve => setTimeout(resolve, 3000));
                        }
                    }
                }
                
                // Final wait and check
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                // Log what we found
                const allDivs = document.querySelectorAll('div').length;
                const allLinks = document.querySelectorAll('a').length;
                const tireKeywords = document.body.innerText.match(/輪胎|tire/gi);
                
                console.log(`Page analysis complete:`);
                console.log(`- Total divs: ${allDivs}`);
                console.log(`- Total links: ${allLinks}`);
                console.log(`- Tire keyword matches: ${tireKeywords ? tireKeywords.length : 0}`);
                console.log(`- Page title: ${document.title}`);
                console.log(`- URL: ${window.location.href}`);
                """
            )
            
            if result.success:
                markdown_text = result.markdown if result.markdown else ""
                
                # Save raw content for debugging
                with open(f"robust_page_{page_num}_content.txt", 'w', encoding='utf-8') as f:
                    f.write(markdown_text)
                
                # Extract tire data
                tires = self.extract_tire_data_comprehensive(markdown_text)
                
                # Also try to find tire patterns in raw text
                tire_mentions = len(re.findall(r'輪胎|tire', markdown_text, re.IGNORECASE))
                
                page_data = {
                    "page_number": page_num,
                    "url": url,
                    "timestamp": datetime.now().isoformat(),
                    "tires_found": len(tires),
                    "tire_mentions": tire_mentions,
                    "content_length": len(markdown_text),
                    "tires": tires
                }
                
                # Add to overall collection
                for tire in tires:
                    tire['source_page'] = page_num
                    tire['source_url'] = url
                    self.all_tire_data.append(tire)
                
                logger.info(f"Page {page_num}: Found {len(tires)} tires, {tire_mentions} tire mentions, {len(markdown_text)} chars")
                return page_data
            else:
                logger.error(f"Failed to crawl page {page_num}: {result.error_message}")
                self.failed_pages.append({"page": page_num, "error": result.error_message})
                return None
                
        except Exception as e:
            logger.error(f"Exception while crawling page {page_num}: {str(e)}")
            self.failed_pages.append({"page": page_num, "error": str(e)})
            return None
    
    async def crawl_pages_robust(self, start_page=0, end_page=22, batch_size=1):
        """Crawl pages with robust approach - one at a time for better success"""
        logger.info(f"Starting robust crawl of pages {start_page} to {end_page}")
        
        all_page_data = []
        
        async with AsyncWebCrawler(verbose=True) as crawler:
            for page_num in range(start_page, end_page + 1):
                logger.info(f"Processing page {page_num}")
                
                result = await self.crawl_page_robust(crawler, page_num)
                
                if result:
                    all_page_data.append(result)
                
                # Save progress after each page
                self.save_intermediate_results(f"robust_progress_page_{page_num}.json")
                
                logger.info(f"Progress: {len(self.all_tire_data)} total tires found so far")
                
                # Wait between pages
                if page_num < end_page:
                    logger.info("Waiting 3 seconds before next page...")
                    await asyncio.sleep(3)
        
        logger.info(f"Robust crawling completed!")
        logger.info(f"Total pages processed: {len(all_page_data)}")
        logger.info(f"Total tires found: {len(self.all_tire_data)}")
        logger.info(f"Failed pages: {len(self.failed_pages)}")
        
        return all_page_data
    
    def save_intermediate_results(self, filename):
        """Save intermediate results"""
        output_data = {
            "crawl_info": {
                "total_tires_found_so_far": len(self.all_tire_data),
                "failed_pages_so_far": len(self.failed_pages),
                "timestamp": datetime.now().isoformat(),
                "target": "48 tires per page × 21 pages = ~1008 tires"
            },
            "failed_pages": self.failed_pages,
            "all_tires": self.all_tire_data
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            logger.info(f"Progress saved to {filename} - {len(self.all_tire_data)} tires found")
        except Exception as e:
            logger.error(f"Error saving progress: {str(e)}")
    
    def save_final_results(self, filename="robust_costco_tire_results.json"):
        """Save final results"""
        output_data = {
            "crawl_summary": {
                "total_pages_attempted": 23,
                "total_tires_found": len(self.all_tire_data),
                "failed_pages_count": len(self.failed_pages),
                "crawl_timestamp": datetime.now().isoformat(),
                "base_url": self.base_url,
                "target_tires": "~1008 (48 per page × 21 pages)",
                "success_rate": f"{len(self.all_tire_data)/1008*100:.1f}%" if len(self.all_tire_data) > 0 else "0%"
            },
            "failed_pages": self.failed_pages,
            "all_tires": self.all_tire_data
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Final results saved to {filename}")
            logger.info(f"Total tires extracted: {len(self.all_tire_data)}")
            
            return filename
            
        except Exception as e:
            logger.error(f"Error saving final results: {str(e)}")
            return None

async def main():
    """Main function"""
    crawler = RobustCostcoTireCrawler()
    
    try:
        # Start with just a few pages to test
        page_data = await crawler.crawl_pages_robust(start_page=0, end_page=2, batch_size=1)
        
        # Save results
        filename = crawler.save_final_results("robust_costco_tire_test_results.json")
        
        # Print summary
        print(f"\n{'='*60}")
        print(f"ROBUST COSTCO TIRE CRAWLING TEST COMPLETED")
        print(f"{'='*60}")
        print(f"Pages tested: 0-2 (3 pages)")
        print(f"Total tires found: {len(crawler.all_tire_data)}")
        print(f"Expected per page: 48")
        print(f"Expected total for 3 pages: 144")
        print(f"Actual: {len(crawler.all_tire_data)}")
        print(f"Success rate: {len(crawler.all_tire_data)/144*100:.1f}%" if len(crawler.all_tire_data) > 0 else "0%")
        
        if crawler.failed_pages:
            print(f"\nFailed pages:")
            for failed in crawler.failed_pages:
                print(f"  Page {failed['page']}: {failed['error']}")
        
        if filename:
            print(f"\nResults saved to: {filename}")
        
        print(f"\n{'='*60}")
        
    except KeyboardInterrupt:
        print("\nCrawling interrupted by user")
        crawler.save_final_results("robust_costco_tire_interrupted.json")
    except Exception as e:
        print(f"Error during crawling: {str(e)}")
        crawler.save_final_results("robust_costco_tire_error.json")

if __name__ == "__main__":
    asyncio.run(main())
