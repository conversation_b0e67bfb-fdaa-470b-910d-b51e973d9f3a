#!/usr/bin/env python3
"""
Simple script to extract all text content from Costco tire page
"""

import asyncio
import json
from datetime import datetime
from crawl4ai import AsyncWebCrawler
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def extract_all_text():
    """Extract all text content from the page"""
    url = "https://www.costco.com.tw/Tire/c/142201?page=0"
    
    async with AsyncWebCrawler(verbose=True) as crawler:
        try:
            logger.info(f"Extracting text from: {url}")
            
            result = await crawler.arun(
                url=url,
                wait_for="networkidle",
                timeout=60000,
                js_code="""
                // Wait for content to load
                await new Promise(resolve => setTimeout(resolve, 8000));
                
                // Scroll to load more content
                window.scrollTo(0, document.body.scrollHeight);
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                // Scroll back up
                window.scrollTo(0, 0);
                await new Promise(resolve => setTimeout(resolve, 2000));
                """
            )
            
            if result.success:
                # Save all text content
                all_text = result.markdown if result.markdown else ""
                
                # Look for tire-related patterns in the text
                tire_patterns = []
                lines = all_text.split('\n')
                
                for i, line in enumerate(lines):
                    line_lower = line.lower()
                    if any(keyword in line_lower for keyword in ['輪胎', 'tire', 'bridgestone', 'michelin', 'continental', 'pirelli', 'goodyear']):
                        # Include context (previous and next lines)
                        start = max(0, i-2)
                        end = min(len(lines), i+3)
                        context = '\n'.join(lines[start:end])
                        tire_patterns.append({
                            'line_number': i,
                            'content': line.strip(),
                            'context': context
                        })
                
                output_data = {
                    "url": url,
                    "timestamp": datetime.now().isoformat(),
                    "full_text": all_text,
                    "tire_related_content": tire_patterns,
                    "html_length": len(result.html) if result.html else 0,
                    "text_length": len(all_text)
                }
                
                # Save to file
                with open("simple_text_extract_result.json", 'w', encoding='utf-8') as f:
                    json.dump(output_data, f, indent=2, ensure_ascii=False)
                
                logger.info("Text extraction completed!")
                logger.info(f"Results saved to: simple_text_extract_result.json")
                
                # Print summary
                print(f"\n=== TEXT EXTRACTION RESULTS ===")
                print(f"URL: {url}")
                print(f"Success: {result.success}")
                print(f"HTML length: {len(result.html) if result.html else 0}")
                print(f"Text length: {len(all_text)}")
                print(f"Tire-related patterns found: {len(tire_patterns)}")
                
                if tire_patterns:
                    print(f"\nFirst few tire-related patterns:")
                    for i, pattern in enumerate(tire_patterns[:5]):
                        print(f"\n{i+1}. Line {pattern['line_number']}: {pattern['content']}")
                        print(f"   Context: {pattern['context'][:200]}...")
                
            else:
                logger.error(f"Failed to extract text: {result.error_message}")
                
        except Exception as e:
            logger.error(f"Exception during extraction: {str(e)}")

if __name__ == "__main__":
    asyncio.run(extract_all_text())
